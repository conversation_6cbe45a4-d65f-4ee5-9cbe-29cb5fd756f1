{"name": "usi-ims", "version": "1.0.0", "description": "Inventory management system", "scripts": {"clear": "lsof -t -i tcp:3002 | xargs kill", "dev": " npm run check && concurrently \"npx tsc --watch\" \"nodemon -q dist/server.js\"", "build": "npm run check && tsc", "start": "node dist/server.js", "migrate-local": "export NODE_ENV=development && npx sequelize-cli db:migrate", "migrate-test": "export NODE_ENV=testing && npx sequelize-cli db:migrate", "migrate-prod": "export NODE_ENV=production && npx sequelize-cli db:migrate", "seed-local": "export NODE_ENV=development && npx sequelize-cli db:seed:all", "seed-test": "export NODE_ENV=testing && npx sequelize-cli db:seed:all", "seed-prod": "export NODE_ENV=production && npx sequelize-cli db:seed:all", "check": "tsc --noEmit"}, "author": "", "license": "ISC", "dependencies": {"cors": "2.8.5", "dotenv": "16.4.7", "express": "4.21.2", "express-rate-limit": "7.5.0", "firebase-admin": "13.0.2", "json-with-bigint": "2.4.1", "lodash": "4.17.21", "morgan": "^1.10.0", "morgan-json": "^1.1.0", "multer": "^2.0.1", "node-cron": "^4.1.0", "pg": "8.13.1", "pg-hstore": "2.3.4", "redis": "4.7.0", "resend": "^4.6.0", "rotating-file-stream": "^3.2.6", "sequelize": "6.37.5", "typescript": "5.7.2", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "3.24.1"}, "devDependencies": {"@types/cors": "2.8.17", "@types/express": "5.0.0", "@types/lodash": "4.17.13", "@types/morgan": "^1.9.10", "@types/morgan-json": "^1.1.3", "@types/multer": "1.4.12", "@types/node": "22.10.2", "@types/node-cron": "^3.0.11", "@types/sequelize": "4.28.20", "@types/uuid": "^10.0.0", "@vitest/ui": "^3.2.3", "concurrently": "9.1.0", "nodemon": "3.1.9", "ts-node": "^10.9.2", "ts-node-dev": "2.0.0", "vitest": "^3.2.3"}}