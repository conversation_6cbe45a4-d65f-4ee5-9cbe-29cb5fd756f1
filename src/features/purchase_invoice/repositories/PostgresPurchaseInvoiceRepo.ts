import { UniqueConstraintError, QueryTypes, Op } from 'sequelize'
import {
  APIBaseResponse,
  PaginatedBaseResponse,
} from '../../../core/CoreInterfaces'
import { HelperMethods } from '../../../core/HelperMethods'
import { SupplierTable } from '../../supplier/database/SupplierTable'
import { PurchaseInvoiceTable } from '../database/PurchaseInvoiceTable'
import {
  IPurchaseInvoiceAddNewItemRequest,
  IPurchaseInvoiceDetailedResponse,
  IPurchaseInvoiceRequest,
  IPurchaseInvoiceResponse,
  IPurchaseInvoiceUpdateRequest,
  IRawMaterialReceivedItemResponse,
} from '../models/IPurchaseInvoice'
import { PURCHASE_INVOICE_STATUS } from '../models/PurchaseInvoiceMisc'
import { IPurchaseInvoiceRepo } from './IPurchaseInvoiceRepo'
import { sequelizeInit } from '../../../sequelize_init'
import { RawMaterialStockInTable } from '../../raw_material_stock/database/RawMaterialStockInTable'
import { RawMaterialStockTable } from '../../raw_material_stock/database/RawMaterialStockTable'
import { ICreateRawMaterialStockIn } from '../../raw_material_stock/models/IRawMaterialStockIn'
import { ICreateRawMaterialRejection, IRawMaterialRejection } from '../../raw_material_stock/models/IRawMaterialRejection'
import { ICreateRawMaterialHold } from '../../raw_material_stock/models/IRawMaterialHold'
import { RawMaterialRejectionTable } from '../../raw_material_stock/database/RawMaterialRejectionTable'
import { RawMaterialHoldTable } from '../../raw_material_stock/database/RawMaterialHoldTable'
import { ICreateDebitNote, IDebitNote } from '../../debit_note/models/IDebitNote'
import { RawMaterialPriceTable } from '../../raw_material/database/RawMaterialPriceTable'
import { DebitNoteTable } from '../../debit_note/database/DebitNoteTable'
import { RawMaterialVariationTable } from '../../raw_material/database/RawMaterialVariationTable'
import { PurchaseInvoiceEntryMappingTable } from '../database/PurchaseInvoiceEntryMappingTable'
import { PurchaseInvoiceConstants } from '../utils/purchase_invoice_constants'
import { DEBIT_NOTE_SOURCE } from '../../debit_note/models/DebitNoteMisc'
import { ItemUnitTable } from '../../item_unit/database/ItemUnitTable'
import { PurchaseInvoicePurchaseOrderMapping } from '../database/PurchaseInvoicePurchaseOrderMapping'
import { PurchaseOrderTable } from '../../purchase_order/database/PurchaseOrderTable'
import { PurchaseOrderItemTable } from '../../purchase_order/database/PurchaseOrderItemTable'
import { ICreateRawMaterialExcessEntry } from '../../raw_material/models/IRawMaterialExcessEntry'
import { ICreateRawMaterialReplacementEntry } from '../../raw_material/models/IRawMaterialReplacementEntry'
import { RawMaterialExcessEntryTable } from '../../raw_material/database/RawMaterialExcessEntry'
import { RawMaterialReplacementEntryTable } from '../../raw_material/database/RawMaterialReplacementEntry'
import { RawMaterialMainTable } from '../../raw_material/database/RawMaterialMainTable'

export class PostgresPurchaseInvoiceRepo implements IPurchaseInvoiceRepo {
  async create(
    purchaseInvoice: IPurchaseInvoiceRequest
  ): Promise<APIBaseResponse<null>> {
    const transaction = await sequelizeInit.transaction()
    try {
      /* save the purchase invoice */
      const invoice = await PurchaseInvoiceTable.create(
        {
          invoiceNumber: purchaseInvoice.invoiceNumber,
          invoiceDate: purchaseInvoice.invoiceDate,
          supplierId: purchaseInvoice.supplierId,
          status: PURCHASE_INVOICE_STATUS.ACTIVE,
          createdById: purchaseInvoice.createdById,
          poNumber: purchaseInvoice.poNumber,
          poDate: purchaseInvoice.poDate,
          purchasedById: purchaseInvoice.purchasedById,
          factoryGateId: purchaseInvoice.factoryGateId,
        },
        {
          transaction: transaction,
          userId: purchaseInvoice.createdById,
        }
      )

      /* get entry id */
      const entrySequence = await sequelizeInit.query(
        `SELECT nextval(:sequenceName)`,
        {
          replacements: {
            sequenceName: PurchaseInvoiceConstants.ENTRY_ID_SEQUENCE_NAME,
          },
          type: QueryTypes.SELECT,
          transaction: transaction,
        }
      )
      const entryId = (entrySequence[0] as any).nextval

      /* save mapping */
      await PurchaseInvoiceEntryMappingTable.create(
        {
          purchaseInvoiceId: invoice.dataValues.id,
          entryNumber: entryId,
        },
        {
          transaction: transaction,
          userId: purchaseInvoice.createdById,
        }
      )
      if (purchaseInvoice.purchaseOrderId) {
        await PurchaseInvoicePurchaseOrderMapping.create({
          purchaseInvoiceId: invoice.dataValues.id,
          purchaseOrderId: purchaseInvoice.purchaseOrderId
        }, { transaction: transaction, userId: purchaseInvoice.createdById })
      }
      /* rejection data */
      const rejectionData: ICreateRawMaterialRejection[] = []
      /* hold data */
      const holdData: ICreateRawMaterialHold[] = []

      /* save the raw material stock in entry */
      const stockInData: ICreateRawMaterialStockIn[] = []

      for (const item of purchaseInvoice.rawMaterials) {
        stockInData.push({
          rawMaterialId: item.rawMaterialId,
          qty: item.totalQty,
          price: item.price,
          storageLocationId: item.storageLocationId,
          factoryGateId: purchaseInvoice.factoryGateId,
          supplierId: purchaseInvoice.supplierId,
          purchaseInvoiceId: invoice.getDataValue('id'),
          createdById: purchaseInvoice.createdById,
        })

        if (item.rejectedQty > 0) {
          rejectionData.push({
            rawMaterialId: item.rawMaterialId,
            purchaseInvoiceId: invoice.getDataValue('id'),
            totalQty: item.totalQty,
            rejectedQty: item.rejectedQty,
            rejectionReason:
              (item.rejectionReason?.trim().length ?? 0) > 0
                ? item.rejectionReason!.trim()
                : null,
            rejectedById: item.rejectedById,
            createdById: purchaseInvoice.createdById,
          })
        }

        if (item.holdQty > 0) {
          holdData.push({
            rawMaterialId: item.rawMaterialId,
            purchaseInvoiceId: invoice.getDataValue('id'),
            totalQty: item.totalQty,
            holdQty: item.holdQty,
            holdReason:
              (item.holdReason?.trim().length ?? 0) > 0
                ? item.holdReason!.trim()
                : null,
            holdById: item.holdById,
            createdById: purchaseInvoice.createdById,
          })
        }

        if (item.excessQty && item.excessQty > 0) {
          const excessData: ICreateRawMaterialExcessEntry = {
            rawMaterialId: item.rawMaterialId,
            purchaseInvoiceId: invoice.getDataValue('id'),
            qty: item.excessQty,
            createdById: purchaseInvoice.createdById,
          }

          await RawMaterialExcessEntryTable.create(excessData, {
            transaction: transaction,
            userId: purchaseInvoice.createdById,
          })
        }
        if (item.replaceableQty && item.replaceableQty > 0) {
          const replaceData: ICreateRawMaterialReplacementEntry = {
            rawMaterialId: item.rawMaterialId,
            purchaseInvoiceId: invoice.getDataValue('id'),
            qty: item.replaceableQty,
            createdById: purchaseInvoice.createdById,
          }
          await RawMaterialReplacementEntryTable.create(replaceData, {
            transaction: transaction,
            userId: purchaseInvoice.createdById,
          })

        }

      }
      await RawMaterialStockInTable.bulkCreate(stockInData, {
        transaction: transaction,
        individualHooks: true,
        userId: purchaseInvoice.createdById,
      })

      /* update the raw material stock */

      /* TODO: optimize this, if possible */

      for (const item of purchaseInvoice.rawMaterials) {
        await RawMaterialStockTable.increment(['totalStock'], {
          by: (item.totalQty + item.excessQty) - (item.rejectedQty + item.holdQty + item.replaceableQty),
          where: {
            rawMaterialId: item.rawMaterialId,
          },
          transaction: transaction,
        })
      }

      /* if required, create the rejection */
      await RawMaterialRejectionTable.bulkCreate(rejectionData, {
        transaction: transaction,
        individualHooks: true,
        userId: purchaseInvoice.createdById,
      })

      /* if required, create the hold */
      await RawMaterialHoldTable.bulkCreate(holdData, {
        transaction: transaction,
        individualHooks: true,
        userId: purchaseInvoice.createdById,
      })

      /* if required create the debit notes */
      const debitNoteData: ICreateDebitNote[] = []
      let actualPrice: number = 0
      let purchasedPrice: number = 0
      let rawMaterial: RawMaterialVariationTable | null
      let rawMaterialPrice: RawMaterialPriceTable | null

      let gstPercentage = 0

      /* TODO: optimize this, if possible */
      for (const item of purchaseInvoice.rawMaterials) {
        /* get raw material */
        rawMaterial = await RawMaterialVariationTable.findByPk(item.rawMaterialId)

        if (!rawMaterial) {
          return HelperMethods.getErrorResponse()
        }

        /* get actual price */
        rawMaterialPrice = await RawMaterialPriceTable.findOne({
          where: {
            supplierId: purchaseInvoice.supplierId,
            rawMaterialId: item.rawMaterialId,
          },
        })

        // gstPercentage = parseFloat(rawMaterial.dataValues.gstPercentage.toString());

        // if (gstPercentage > 0) {
        //     actualPrice = Number((
        //         parseFloat(rawMaterialPrice!.dataValues.price.toString())
        //         + (parseFloat(rawMaterial.dataValues.gstPercentage.toString()) / 100)
        //         * (parseFloat(rawMaterialPrice!.dataValues.price.toString()))
        //     ).toFixed(2));
        // }
        // else {
        actualPrice = Number(rawMaterialPrice!.dataValues.price.toString())
        // }

        // purchasedPrice = Number((item.price / item.totalQty).toFixed(2));
        purchasedPrice = Number(item.price)

        if (actualPrice < purchasedPrice) {
          debitNoteData.push({
            purchaseInvoiceId: invoice.dataValues.id,
            qty: item.totalQty - (item.rejectedQty + item.holdQty),
            rawMaterialId: item.rawMaterialId,
            actualPrice: actualPrice,
            purchasedPrice: purchasedPrice,
            source: DEBIT_NOTE_SOURCE.PRICE_MISMATCH,
            createdById: purchaseInvoice.createdById,
            note: (item.rejectionReason?.trim().length ?? 0) > 0
              ? item.rejectionReason!.trim()
              : null,
            is_manual: false,
          })

          /* rejected Items  */
          if (item.rejectedQty > 0) {
            debitNoteData.push({
              purchaseInvoiceId: invoice.dataValues.id,
              qty: item.rejectedQty,
              rawMaterialId: item.rawMaterialId,
              actualPrice: actualPrice,
              purchasedPrice: purchasedPrice,
              source: DEBIT_NOTE_SOURCE.PRICE_MISMATCH_AND_REJECTED,
              createdById: purchaseInvoice.createdById,
              note: (item.rejectionReason?.trim().length ?? 0) > 0
                ? item.rejectionReason!.trim()
                : null,
              is_manual: false,
            })
          }
        } else if (item.rejectedQty > 0) {
          /* rejected Items  */
          debitNoteData.push({
            purchaseInvoiceId: invoice.dataValues.id,
            qty: item.rejectedQty,
            rawMaterialId: item.rawMaterialId,
            actualPrice: actualPrice,
            purchasedPrice: purchasedPrice,
            source: DEBIT_NOTE_SOURCE.REJECTED,
            createdById: purchaseInvoice.createdById,
            note: (item.rejectionReason?.trim().length ?? 0) > 0
              ? item.rejectionReason!.trim()
              : null,
            is_manual: false,
          })
        }
      }

      for (const item of debitNoteData) {
        await DebitNoteTable.create(item, {
          transaction: transaction,
          userId: purchaseInvoice.createdById,
        })
      }

      await transaction.commit()

      return HelperMethods.getSuccessResponse(null)
    } catch (error) {
      await transaction.rollback()
      HelperMethods.handleError(error)

      if (error instanceof UniqueConstraintError) {
        if (error.errors[0].path === 'invoiceNumber') {
          return HelperMethods.getErrorResponse('Invoice number already exists')
        } else if (error.errors[0].path === 'poNumber') {
          return HelperMethods.getErrorResponse('PO number already exists')
        }
      }
      return HelperMethods.getErrorResponse()
    }
  }

  async update(
    request: IPurchaseInvoiceUpdateRequest
  ): Promise<APIBaseResponse<null>> {
    const transaction = await sequelizeInit.transaction()
    try {
      /* get invoice */

      const oldInvoice = await PurchaseInvoiceTable.findByPk(request.id)

      if (!oldInvoice) {
        return HelperMethods.getErrorResponse('No purchase invoice found')
      }

      // /* delete rejections */
      // await RawMaterialRejectionTable.destroy({
      //     where: {
      //         purchaseInvoiceId: request.id,
      //     },
      //     transaction: transaction,
      //     userId: request.updatedById,
      // });
      // /* delete hold */
      // await RawMaterialHoldTable.destroy({
      //     where: {
      //         purchaseInvoiceId: request.id,
      //     },
      //     transaction: transaction,
      //     userId: request.updatedById,
      // });

      // /* decrement stock */

      // const oldStockInData = await RawMaterialStockInTable.findAll({
      //     where: {
      //         purchaseInvoiceId: request.id,
      //     },
      //     transaction: transaction,
      // });

      // for (const item of oldStockInData) {
      //     await RawMaterialStockTable.decrement(["totalStock"], {
      //         by: item.dataValues.qty,
      //         where: {
      //             rawMaterialId: item.dataValues.rawMaterialId,
      //         },
      //         transaction: transaction,
      //     });
      // }

      // /* delete stock in  */
      // await RawMaterialStockInTable.destroy({
      //     where: {
      //         purchaseInvoiceId: request.id
      //     },
      //     transaction: transaction,
      //     userId: request.updatedById
      // });

      // /* delete debit notes */
      // await DebitNoteTable.destroy({
      //     where: {
      //         purchaseInvoiceId: request.id
      //     },
      //     transaction: transaction,
      //     userId: request.updatedById
      // });

      // /* update the purchase invoice */
      // await PurchaseInvoiceTable.update({
      //     invoiceNumber: request.invoiceNumber,
      //     invoiceDate: request.invoiceDate,
      //     supplierId: request.supplierId,
      //     updatedById: request.updatedById,
      //     poNumber: request.poNumber,
      //     poDate: request.poDate,
      //     purchasedById: request.purchasedById,
      //     factoryGateId: request.factoryGateId,
      // }, {
      //     where: {
      //         id: request.id,
      //     },
      //     transaction: transaction,
      //     userId: request.updatedById,
      // });

      /* rejection data */
      const rejectionData: ICreateRawMaterialRejection[] = []
      /* hold data */
      const holdData: ICreateRawMaterialHold[] = []

      /* save the raw material stock in entry */
      const stockInData: ICreateRawMaterialStockIn[] = []

      for (const item of request.rawMaterials) {
        stockInData.push({
          rawMaterialId: item.rawMaterialId,
          qty: item.totalQty,
          price: item.price,
          storageLocationId: item.storageLocationId,
          factoryGateId: request.factoryGateId,
          supplierId: request.supplierId,
          purchaseInvoiceId: request.id,
          createdById: request.updatedById,
        })

        if (item.rejectedQty > 0) {
          rejectionData.push({
            rawMaterialId: item.rawMaterialId,
            purchaseInvoiceId: request.id,
            totalQty: item.totalQty,
            rejectedQty: item.rejectedQty,
            rejectionReason:
              (item.rejectionReason?.trim().length ?? 0) > 0
                ? item.rejectionReason!.trim()
                : null,
            rejectedById: item.rejectedById,
            createdById: request.updatedById,
          })
        }

        if (item.holdQty > 0) {
          holdData.push({
            rawMaterialId: item.rawMaterialId,
            purchaseInvoiceId: request.id,
            totalQty: item.totalQty,
            holdQty: item.holdQty,
            holdReason:
              (item.holdReason?.trim().length ?? 0) > 0
                ? item.holdReason!.trim()
                : null,
            holdById: item.holdById,
            createdById: request.updatedById,
          })
        }

        if (item.excessQty && item.excessQty > 0) {
          const checkIfExists = await RawMaterialExcessEntryTable.findOne({
            where: {
              purchaseInvoiceId: request.id,
              rawMaterialId: item.rawMaterialId
            }
          })
          if (!checkIfExists) {

            const excessData: ICreateRawMaterialExcessEntry = {
              rawMaterialId: item.rawMaterialId,
              purchaseInvoiceId: request.id,
              qty: item.excessQty,
              createdById: request.updatedById,
            }

            await RawMaterialExcessEntryTable.create(excessData, {
              transaction: transaction,
              userId: request.updatedById,
            })
          } else {
            await checkIfExists.update({ qty: item.excessQty }, { transaction: transaction });
          }
        }
        if (item.replaceableQty && item.replaceableQty > 0) {
          const checkIfExists = await RawMaterialReplacementEntryTable.findOne({
            where: {
              purchaseInvoiceId: request.id,
              rawMaterialId: item.rawMaterialId
            }
          })
          if (!checkIfExists) {
            const replaceData: ICreateRawMaterialReplacementEntry = {
              rawMaterialId: item.rawMaterialId,
              purchaseInvoiceId: request.id,
              qty: item.replaceableQty,
              createdById: request.updatedById,
            }
            await RawMaterialReplacementEntryTable.create(replaceData, {
              transaction: transaction,
              userId: request.updatedById,
            })
          } else {
            await checkIfExists.update({ qty: item.replaceableQty }, { transaction: transaction });
          }
        }

      }
      await RawMaterialStockInTable.bulkCreate(stockInData, {
        transaction: transaction,
        individualHooks: true,
        userId: request.updatedById,
      })

      /* update the raw material stock */

      /* TODO: optimize this, if possible */

      for (const item of request.rawMaterials) {
        await RawMaterialStockTable.increment(['totalStock'], {
          by: (item.totalQty + item.excessQty) - (item.rejectedQty + item.holdQty + item.replaceableQty),
          where: {
            rawMaterialId: item.rawMaterialId,
          },
          transaction: transaction,
        })
      }

      /* if required, create the rejection */
      await RawMaterialRejectionTable.bulkCreate(rejectionData, {
        transaction: transaction,
        individualHooks: true,
        userId: request.updatedById,
      })

      /* if required, create the hold */
      await RawMaterialHoldTable.bulkCreate(holdData, {
        transaction: transaction,
        individualHooks: true,
        userId: request.updatedById,
      })

      /* if required create the debit notes */
      const debitNoteData: ICreateDebitNote[] = []
      let actualPrice: number = 0
      let purchasedPrice: number = 0
      let rawMaterial: RawMaterialVariationTable | null
      let rawMaterialPrice: RawMaterialPriceTable | null

      let gstPercentage = 0

      /* TODO: optimize this, if possible */
      for (const item of request.rawMaterials) {
        /* get raw material */
        rawMaterial = await RawMaterialVariationTable.findByPk(item.rawMaterialId)

        if (!rawMaterial) {
          return HelperMethods.getErrorResponse()
        }

        /* get actual price */
        rawMaterialPrice = await RawMaterialPriceTable.findOne({
          where: {
            supplierId: request.supplierId,
            rawMaterialId: item.rawMaterialId,
          },
        })

        // gstPercentage = parseFloat(rawMaterial.dataValues.gstPercentage.toString());

        // if (gstPercentage > 0) {
        //     actualPrice = Number((
        //         parseFloat(rawMaterialPrice!.dataValues.price.toString())
        //         + (parseFloat(rawMaterial.dataValues.gstPercentage.toString()) / 100)
        //         * (parseFloat(rawMaterialPrice!.dataValues.price.toString()))
        //     ).toFixed(2));
        // }
        // else {
        actualPrice = Number(rawMaterialPrice!.dataValues.price.toString())
        // }

        // purchasedPrice = Number((item.price / item.totalQty).toFixed(2));
        purchasedPrice = Number(item.price)

        if (actualPrice < purchasedPrice) {
          debitNoteData.push({
            purchaseInvoiceId: oldInvoice.dataValues.id,
            qty: item.totalQty - (item.rejectedQty + item.holdQty),
            rawMaterialId: item.rawMaterialId,
            actualPrice: actualPrice,
            purchasedPrice: purchasedPrice,
            source: DEBIT_NOTE_SOURCE.PRICE_MISMATCH,
            createdById: request.updatedById,
            note: (item.rejectionReason?.trim().length ?? 0) > 0
              ? item.rejectionReason!.trim()
              : null,
            is_manual: false,
          })

          /* rejected Items  */
          if (item.rejectedQty > 0) {
            debitNoteData.push({
              purchaseInvoiceId: oldInvoice.dataValues.id,
              qty: item.rejectedQty,
              rawMaterialId: item.rawMaterialId,
              actualPrice: actualPrice,
              purchasedPrice: purchasedPrice,
              source: DEBIT_NOTE_SOURCE.PRICE_MISMATCH_AND_REJECTED,
              createdById: request.updatedById,
              note: (item.rejectionReason?.trim().length ?? 0) > 0
                ? item.rejectionReason!.trim()
                : null,
              is_manual: false,
            })
          }
        } else if (item.rejectedQty > 0) {
          /* rejected Items  */
          debitNoteData.push({
            purchaseInvoiceId: oldInvoice.dataValues.id,
            qty: item.rejectedQty,
            rawMaterialId: item.rawMaterialId,
            actualPrice: actualPrice,
            purchasedPrice: purchasedPrice,
            source: DEBIT_NOTE_SOURCE.REJECTED,
            createdById: request.updatedById,
            note: (item.rejectionReason?.trim().length ?? 0) > 0
              ? item.rejectionReason!.trim()
              : null,
            is_manual: false,
          })
        }
      }

      for (const item of debitNoteData) {
        await DebitNoteTable.create(item, {
          transaction: transaction,
          userId: request.updatedById,
        })
      }

      await transaction.commit()

      return HelperMethods.getSuccessResponse(null)
    } catch (error) {
      await transaction.rollback()
      HelperMethods.handleError(error)

      if (error instanceof UniqueConstraintError) {
        if (error.errors[0].path === 'invoiceNumber') {
          return HelperMethods.getErrorResponse('Invoice number already exists')
        } else if (error.errors[0].path === 'poNumber') {
          return HelperMethods.getErrorResponse('PO number already exists')
        }
      }
      return HelperMethods.getErrorResponse()
    }
  }

  async edit(
    request: IPurchaseInvoiceUpdateRequest
  ): Promise<APIBaseResponse<null>> {
    const transaction = await sequelizeInit.transaction()
    try {
      /* get invoice */
      const oldInvoice = await PurchaseInvoiceTable.findByPk(request.id, {
        include: [
          {
            model: RawMaterialRejectionTable,
            as: 'rawMaterialRejections',
          },
          {
            model: RawMaterialHoldTable,
            as: 'rawMaterialHolds',
          },
          {
            model: RawMaterialExcessEntryTable,
            as: 'rawMaterialExcessEntry',
            required: false
          },
          {
            model: RawMaterialReplacementEntryTable,
            as: 'rawMaterialReplacementEntry',
            required: false
          }
        ],
      })

      if (!oldInvoice) {
        return HelperMethods.getErrorResponse('No purchase invoice found')
      }

      /* delete rejections */
      await RawMaterialRejectionTable.update(
        {
          deletedAt: new Date(),
          deletedById: request.updatedById,
        },
        {
          where: {
            purchaseInvoiceId: request.id,
          },
          transaction: transaction,
          userId: request.updatedById,
          individualHooks: true,
        }
      )

      /* delete hold */
      await RawMaterialHoldTable.update(
        {
          deletedAt: new Date(),
          deletedById: request.updatedById,
        },
        {
          where: {
            purchaseInvoiceId: request.id,
          },
          transaction: transaction,
          userId: request.updatedById,
          individualHooks: true,
        }
      )

      //delete excess
      await RawMaterialExcessEntryTable.update(
        {
          deletedAt: new Date(),
          deletedById: request.updatedById,
        },
        {
          where: {
            purchaseInvoiceId: request.id,
          },
          transaction: transaction,
          userId: request.updatedById,
          individualHooks: true,
        }
      )
      //delete replacement
      await RawMaterialReplacementEntryTable.update(
        {
          deletedAt: new Date(),
          deletedById: request.updatedById,
        },
        {
          where: {
            purchaseInvoiceId: request.id,
          },
          transaction: transaction,
          userId: request.updatedById,
          individualHooks: true,
        }
      )
      /* delete debit notes */
      await DebitNoteTable.update(
        {
          deletedAt: new Date(),
          deletedById: request.updatedById,
        },
        {
          where: {
            purchaseInvoiceId: request.id,
          },
          transaction: transaction,
          userId: request.updatedById,
          individualHooks: true,
        }
      )

      /* decrement stock */
      const oldStockInData = await RawMaterialStockInTable.findAll({
        where: {
          purchaseInvoiceId: request.id,
        },
        transaction: transaction,
      })

      let rejectedQty = 0
      let holdQty = 0
      let excessQty = 0;
      let replaceQty = 0;

      for (const item of oldStockInData) {
        rejectedQty = parseFloat(
          (
            oldInvoice.rawMaterialRejections.find(
              (x) =>
                x.dataValues.rawMaterialId === item.dataValues.rawMaterialId
            )?.dataValues.rejectedQty ?? 0
          ).toString()
        )

        holdQty = parseFloat(
          (
            oldInvoice.rawMaterialHolds.find(
              (x) =>
                x.dataValues.rawMaterialId === item.dataValues.rawMaterialId
            )?.dataValues.holdQty ?? 0
          ).toString()
        )

        excessQty = parseFloat(
          (
            oldInvoice.rawMaterialExcessEntry.find(
              (x) =>
                x.dataValues.rawMaterialId === item.dataValues.rawMaterialId
            )?.dataValues.qty ?? 0
          ).toString()
        )

        replaceQty = parseFloat(
          (
            oldInvoice.rawMaterialReplacementEntry.find(
              (x) =>
                x.dataValues.rawMaterialId === item.dataValues.rawMaterialId
            )?.dataValues.qty ?? 0
          ).toString()
        )
        /* get current stock*/
        const currentStock = await RawMaterialStockTable.findOne({
          where: {
            rawMaterialId: item.dataValues.rawMaterialId
          },
          transaction: transaction
        });
        if (!currentStock) {
          return HelperMethods.getErrorResponse("Raw Material Stock not found");
        }
        const targetQty = (item.dataValues.qty + excessQty) - (rejectedQty + holdQty + replaceQty);
        if (item.dataValues.storageLocationId == null) {
          if (currentStock.dataValues.totalStock >= targetQty) {
            await RawMaterialStockTable.decrement(['totalStock'], {
              by: item.dataValues.qty + excessQty - (rejectedQty + holdQty + replaceQty),
              where: {
                rawMaterialId: item.dataValues.rawMaterialId,
              },
              transaction: transaction,
            });
          }
        } else {
          if (currentStock.dataValues.totalStock >= targetQty && currentStock.dataValues.usableStock >= targetQty) {
            await RawMaterialStockTable.decrement(['totalStock', 'usableStock'], {
              by: item.dataValues.qty + excessQty - (rejectedQty + holdQty + replaceQty),
              where: {
                rawMaterialId: item.dataValues.rawMaterialId,
              },
              transaction: transaction,
            });
          }
        }
      }

      /* delete stock in  */
      await RawMaterialStockInTable.update(
        {
          deletedAt: new Date(),
          storageLocationId: null,
          deletedById: request.updatedById,
        },
        {
          where: {
            purchaseInvoiceId: request.id,
          },
          transaction: transaction,
          userId: request.updatedById,
          individualHooks: true,
        }
      )

      /* update the purchase invoice */
      await PurchaseInvoiceTable.update(
        {
          invoiceNumber: request.invoiceNumber,
          invoiceDate: request.invoiceDate,
          supplierId: request.supplierId,
          poNumber: request.poNumber,
          poDate: request.poDate,
          purchasedById: request.purchasedById,
          factoryGateId: request.factoryGateId,
          updatedById: request.updatedById,
        },
        {
          where: {
            id: request.id,
          },
          transaction: transaction,
          userId: request.updatedById,
        }
      )

      /* rejection data */
      const rejectionData: ICreateRawMaterialRejection[] = []
      /* hold data */
      const holdData: ICreateRawMaterialHold[] = []

      /* save the raw material stock in entry */
      const stockInData: ICreateRawMaterialStockIn[] = []

      const excessData: ICreateRawMaterialExcessEntry[] = []

      const replaceData: ICreateRawMaterialReplacementEntry[] = []

      for (const item of request.rawMaterials) {
        stockInData.push({
          rawMaterialId: item.rawMaterialId,
          qty: item.totalQty,
          price: item.price,
          storageLocationId: null,
          factoryGateId: request.factoryGateId,
          supplierId: request.supplierId,
          purchaseInvoiceId: request.id,
          createdById: request.updatedById,
        })

        if (item.rejectedQty > 0) {
          rejectionData.push({
            rawMaterialId: item.rawMaterialId,
            purchaseInvoiceId: request.id,
            totalQty: item.totalQty,
            rejectedQty: item.rejectedQty,
            rejectionReason:
              (item.rejectionReason?.trim().length ?? 0) > 0
                ? item.rejectionReason!.trim()
                : null,
            rejectedById: item.rejectedById,
            createdById: request.updatedById,
          })
        }

        if (item.holdQty > 0) {
          holdData.push({
            rawMaterialId: item.rawMaterialId,
            purchaseInvoiceId: request.id,
            totalQty: item.totalQty,
            holdQty: item.holdQty,
            holdReason:
              (item.holdReason?.trim().length ?? 0) > 0
                ? item.holdReason!.trim()
                : null,
            holdById: item.holdById,
            createdById: request.updatedById,
          })
        }

        if (item.excessQty && item.excessQty > 0) {
          excessData.push({
            rawMaterialId: item.rawMaterialId,
            purchaseInvoiceId: request.id,
            qty: item.excessQty,
            createdById: request.updatedById,
          })
        }
        if (item.replaceableQty && item.replaceableQty > 0) {
          replaceData.push({
            rawMaterialId: item.rawMaterialId,
            purchaseInvoiceId: request.id,
            qty: item.replaceableQty,
            createdById: request.updatedById,
          })
        }
      }

      await RawMaterialStockInTable.bulkCreate(stockInData, {
        transaction: transaction,
        individualHooks: true,
        userId: request.updatedById,
      })

      /* update the raw material stock */

      /* TODO: optimize this, if possible */

      for (const item of request.rawMaterials) {
        await RawMaterialStockTable.increment(['totalStock'], {
          by: (item.totalQty + item.excessQty) - (item.rejectedQty + item.holdQty + item.replaceableQty),
          where: {
            rawMaterialId: item.rawMaterialId,
          },
          transaction: transaction,
        })
      }

      /* if required, create the rejection */
      await RawMaterialRejectionTable.bulkCreate(rejectionData, {
        transaction: transaction,
        individualHooks: true,
        userId: request.updatedById,
      })

      /* if required, create the hold */
      await RawMaterialHoldTable.bulkCreate(holdData, {
        transaction: transaction,
        individualHooks: true,
        userId: request.updatedById,
      })
      /* if required, create the excess */
      await RawMaterialExcessEntryTable.bulkCreate(excessData, {
        transaction: transaction,
        individualHooks: true,
        userId: request.updatedById,
      })
      /* if required, create the replace */
      await RawMaterialReplacementEntryTable.bulkCreate(replaceData, {
        transaction: transaction,
        individualHooks: true,
        userId: request.updatedById,
      })
      /* if required create the debit notes */
      const debitNoteData: ICreateDebitNote[] = []
      let actualPrice: number = 0
      let purchasedPrice: number = 0
      let rawMaterial: RawMaterialVariationTable | null
      let rawMaterialPrice: RawMaterialPriceTable | null

      let gstPercentage = 0

      /* TODO: optimize this, if possible */
      for (const item of request.rawMaterials) {
        /* get raw material */
        rawMaterial = await RawMaterialVariationTable.findByPk(item.rawMaterialId)

        if (!rawMaterial) {
          return HelperMethods.getErrorResponse()
        }

        /* get actual price */
        rawMaterialPrice = await RawMaterialPriceTable.findOne({
          where: {
            supplierId: request.supplierId,
            rawMaterialId: item.rawMaterialId,
          },
        })

        // gstPercentage = parseFloat(rawMaterial.dataValues.gstPercentage.toString());

        // if (gstPercentage > 0) {
        //     actualPrice = Number((
        //         parseFloat(rawMaterialPrice!.dataValues.price.toString())
        //         + (parseFloat(rawMaterial.dataValues.gstPercentage.toString()) / 100)
        //         * (parseFloat(rawMaterialPrice!.dataValues.price.toString()))
        //     ).toFixed(2));
        // }
        // else {
        actualPrice = Number(rawMaterialPrice!.dataValues.price.toString())
        // }

        // purchasedPrice = Number((item.price / item.totalQty).toFixed(2));
        purchasedPrice = Number(item.price)

        if (actualPrice < purchasedPrice) {
          debitNoteData.push({
            purchaseInvoiceId: oldInvoice.dataValues.id,
            qty: item.totalQty - (item.rejectedQty + item.holdQty),
            rawMaterialId: item.rawMaterialId,
            actualPrice: actualPrice,
            purchasedPrice: purchasedPrice,
            source: DEBIT_NOTE_SOURCE.PRICE_MISMATCH,
            createdById: request.updatedById,
            note: null,
            is_manual: false,
          })

          /* rejected Items  */
          if (item.rejectedQty > 0) {
            debitNoteData.push({
              purchaseInvoiceId: oldInvoice.dataValues.id,
              qty: item.rejectedQty,
              rawMaterialId: item.rawMaterialId,
              actualPrice: actualPrice,
              purchasedPrice: purchasedPrice,
              source: DEBIT_NOTE_SOURCE.PRICE_MISMATCH_AND_REJECTED,
              createdById: request.updatedById,
              note: null,
              is_manual: false,
            })
          }
        } else if (item.rejectedQty > 0) {
          /* rejected Items  */
          debitNoteData.push({
            purchaseInvoiceId: oldInvoice.dataValues.id,
            qty: item.rejectedQty,
            rawMaterialId: item.rawMaterialId,
            actualPrice: actualPrice,
            purchasedPrice: purchasedPrice,
            source: DEBIT_NOTE_SOURCE.REJECTED,
            createdById: request.updatedById,
            note: null,
            is_manual: false,
          })
        }
      }

      for (const item of debitNoteData) {
        await DebitNoteTable.create(item, {
          transaction: transaction,
          userId: request.updatedById,
        })
      }

      await transaction.commit()

      return HelperMethods.getSuccessResponse(null)
    } catch (error) {
      await transaction.rollback()
      HelperMethods.handleError(error)

      if (error instanceof UniqueConstraintError) {
        if (error.errors[0].path === 'invoiceNumber') {
          return HelperMethods.getErrorResponse('Invoice number already exists')
        } else if (error.errors[0].path === 'poNumber') {
          return HelperMethods.getErrorResponse('PO number already exists')
        }
      }
      return HelperMethods.getErrorResponse()
    }
  }

  async addNewItem(
    request: IPurchaseInvoiceAddNewItemRequest
  ): Promise<APIBaseResponse<null>> {
    const transaction = await sequelizeInit.transaction()
    try {
      /* get the purchase invoice */
      const purchaseInvoice = await PurchaseInvoiceTable.findByPk(request.id, {
        include: {
          model: RawMaterialStockInTable,
          as: 'rawMaterialStockInEntries',
        },
      })

      if (!purchaseInvoice) {
        return HelperMethods.getErrorResponse('Purchase invoice not found')
      }

      /* update the purchase invoice */
      const invoice = await PurchaseInvoiceTable.update(
        {
          updatedAt: new Date(),
          updatedById: request.updatedById,
        },
        {
          where: {
            id: request.id,
          },
          transaction: transaction,
          userId: request.updatedById,
          individualHooks: true,
        }
      )

      /* rejection data */
      const rejectionData: ICreateRawMaterialRejection[] = []
      /* hold data */
      const holdData: ICreateRawMaterialHold[] = []

      /* save the raw material stock in entry */
      const stockInData: ICreateRawMaterialStockIn[] = []

      for (const item of request.rawMaterials) {
        stockInData.push({
          rawMaterialId: item.rawMaterialId,
          qty: item.totalQty,
          price: item.price,
          storageLocationId: item.storageLocationId,
          factoryGateId:
            purchaseInvoice.rawMaterialStockInEntries[0].dataValues
              .factoryGateId,
          supplierId:
            purchaseInvoice.rawMaterialStockInEntries[0].dataValues.supplierId,
          purchaseInvoiceId: purchaseInvoice.dataValues.id,
          createdById: request.updatedById,
        })

        if (item.rejectedQty > 0) {
          rejectionData.push({
            rawMaterialId: item.rawMaterialId,
            purchaseInvoiceId: purchaseInvoice.dataValues.id,
            totalQty: item.totalQty,
            rejectedQty: item.rejectedQty,
            rejectionReason:
              (item.rejectionReason?.trim().length ?? 0) > 0
                ? item.rejectionReason!.trim()
                : null,
            rejectedById: item.rejectedById,
            createdById: request.updatedById,
          })
        }

        if (item.holdQty > 0) {
          holdData.push({
            rawMaterialId: item.rawMaterialId,
            purchaseInvoiceId: purchaseInvoice.dataValues.id,
            totalQty: item.totalQty,
            holdQty: item.holdQty,
            holdReason:
              (item.holdReason?.trim().length ?? 0) > 0
                ? item.holdReason!.trim()
                : null,
            holdById: item.holdById,
            createdById: request.updatedById,
          })
        }
      }
      await RawMaterialStockInTable.bulkCreate(stockInData, {
        transaction: transaction,
        individualHooks: true,
        userId: request.updatedById,
      })

      /* update the raw material stock */

      /* TODO: optimize this, if possible */

      for (const item of request.rawMaterials) {
        await RawMaterialStockTable.increment('totalStock', {
          by: item.totalQty - (item.rejectedQty + item.holdQty),
          where: {
            rawMaterialId: item.rawMaterialId,
          },
          transaction: transaction,
        })
      }

      /* if required, create the rejection */
      await RawMaterialRejectionTable.bulkCreate(rejectionData, {
        transaction: transaction,
        individualHooks: true,
        userId: request.updatedById,
      })

      /* if required, create the hold */
      await RawMaterialHoldTable.bulkCreate(holdData, {
        transaction: transaction,
        individualHooks: true,
        userId: request.updatedById,
      })

      /* if required create the debit notes */
      const debitNoteData: ICreateDebitNote[] = []
      let actualPrice: number = 0
      let purchasedPrice: number = 0
      let rawMaterial: RawMaterialVariationTable | null
      let rawMaterialPrice: RawMaterialPriceTable | null

      let gstPercentage = 0

      /* TODO: optimize this, if possible */
      for (const item of request.rawMaterials) {
        /* get raw material */
        rawMaterial = await RawMaterialVariationTable.findByPk(item.rawMaterialId)

        if (!rawMaterial) {
          return HelperMethods.getErrorResponse()
        }

        /* get actual price */
        rawMaterialPrice = await RawMaterialPriceTable.findOne({
          where: {
            supplierId:
              purchaseInvoice.rawMaterialStockInEntries[0].dataValues
                .supplierId,
            rawMaterialId: item.rawMaterialId,
          },
        })

        // gstPercentage = parseFloat(rawMaterial.dataValues.gstPercentage.toString());

        // if (gstPercentage > 0) {
        //     actualPrice = Number((
        //         parseFloat(rawMaterialPrice!.dataValues.price.toString())
        //         + (parseFloat(rawMaterial.dataValues.gstPercentage.toString()) / 100)
        //         * (parseFloat(rawMaterialPrice!.dataValues.price.toString()))
        //     ).toFixed(2));
        // }
        // else {
        actualPrice = Number(rawMaterialPrice!.dataValues.price.toString())
        // }

        // purchasedPrice = Number((item.price / item.totalQty).toFixed(2));
        purchasedPrice = Number(item.price)

        if (actualPrice < purchasedPrice) {
          debitNoteData.push({
            purchaseInvoiceId: purchaseInvoice.dataValues.id,
            qty: item.totalQty,
            rawMaterialId: item.rawMaterialId,
            actualPrice: actualPrice,
            purchasedPrice: purchasedPrice,
            source: DEBIT_NOTE_SOURCE.PRICE_MISMATCH,
            createdById: request.updatedById,
            note: null,
            is_manual: false,
          })
        }
      }

      for (const item of debitNoteData) {
        await DebitNoteTable.create(item, {
          transaction: transaction,
          userId: request.updatedById,
        })
      }

      await transaction.commit()

      return HelperMethods.getSuccessResponse(null)
    } catch (error) {
      await transaction.rollback()
      HelperMethods.handleError(error)
      return HelperMethods.getErrorResponse()
    }
  }

  async getAll(
    page: number,
    pageSize: number
  ): Promise<
    APIBaseResponse<PaginatedBaseResponse<IPurchaseInvoiceResponse> | null>
  > {
    try {
      const offset = (page - 1) * pageSize
      const { count, rows } = await PurchaseInvoiceTable.findAndCountAll({
        limit: pageSize,
        offset: offset,
        order: [['invoiceDate', 'DESC']],
        where: {
          status: PURCHASE_INVOICE_STATUS.ACTIVE,
        },
        include: [
          {
            model: SupplierTable,
            attributes: ['name'],
            as: 'supplier',
          },

          {
            model: PurchaseInvoiceEntryMappingTable,
            as: 'entryMapping',
            attributes: ['entryNumber'],
          },
          {
            model: RawMaterialExcessEntryTable,
            as: 'rawMaterialExcessEntry',
            required: false
          },
          {
            model: RawMaterialReplacementEntryTable,
            as: 'rawMaterialReplacementEntry',
            required: false
          }
        ],
      })

      const data: IPurchaseInvoiceResponse[] = []

      for (const item of rows) {
        data.push({
          ...item.get(),
          factoryGateId: item.dataValues.factoryGateId,
          supplier: item.supplier.dataValues.name,
          entryId: item.entryMapping?.dataValues.entryNumber ?? '',
        })
      }

      const totalPages = Math.ceil(count / pageSize)

      return HelperMethods.getSuccessResponse({
        currentPage: page,
        totalData: count,
        totalPages: totalPages,
        data: data,
      })
    } catch (error) {
      HelperMethods.handleError(error)
      return HelperMethods.getErrorResponse()
    }
  }

  async getByDateRange(
    startDate: Date,
    endDate: Date,
    page: number,
    pageSize: number
  ): Promise<
    APIBaseResponse<PaginatedBaseResponse<IPurchaseInvoiceResponse> | null>
  > {
    try {
      const offset = (page - 1) * pageSize
      const { count, rows } = await PurchaseInvoiceTable.findAndCountAll({
        limit: pageSize,
        offset: offset,
        order: [['invoiceDate', 'DESC']],
        where: {
          invoiceDate: {
            [Op.between]: [startDate, endDate],
          },

          status: PURCHASE_INVOICE_STATUS.ACTIVE,
        },
        include: [
          {
            model: SupplierTable,
            attributes: ['name'],
            as: 'supplier',
          },

          {
            model: PurchaseInvoiceEntryMappingTable,
            as: 'entryMapping',
            attributes: ['entryNumber'],
          },
          {
            model: RawMaterialExcessEntryTable,
            as: 'rawMaterialExcessEntry',
            required: false
          },
          {
            model: RawMaterialReplacementEntryTable,
            as: 'rawMaterialReplacementEntry',
            required: false
          }
        ],
      })

      const data: IPurchaseInvoiceResponse[] = []

      for (const item of rows) {
        data.push({
          ...item.get(),
          factoryGateId: item.dataValues.factoryGateId,
          supplier: item.supplier.dataValues.name,
          entryId: item.entryMapping?.dataValues.entryNumber ?? '',
        })
      }

      const totalPages = Math.ceil(count / pageSize)

      return HelperMethods.getSuccessResponse({
        currentPage: page,
        totalData: count,
        totalPages: totalPages,
        data: data,
      })
    } catch (error) {
      HelperMethods.handleError(error)
      return HelperMethods.getErrorResponse()
    }
  }

  async searchByAny(
    text: string,
    page: number,
    pageSize: number
  ): Promise<
    APIBaseResponse<PaginatedBaseResponse<IPurchaseInvoiceResponse> | null>
  > {
    try {
      const offset = (page - 1) * pageSize

      const { count, rows } = await PurchaseInvoiceTable.findAndCountAll({
        limit: pageSize,
        offset: offset,
        order: [['createdAt', 'DESC']],

        include: [
          {
            model: SupplierTable,
            attributes: ['name'],
            as: 'supplier',
            required: true
          },

          {
            model: PurchaseInvoiceEntryMappingTable,
            as: 'entryMapping',
            attributes: ['entryNumber'],
          },
          {
            model: RawMaterialExcessEntryTable,
            as: 'rawMaterialExcessEntry',
            required: false
          },
          {
            model: RawMaterialReplacementEntryTable,
            as: 'rawMaterialReplacementEntry',
            required: false
          }
        ],

        where: {
          status: PURCHASE_INVOICE_STATUS.ACTIVE,
          [Op.or]: [
            {
              poNumber: {
                [Op.iLike]: `%${text}%`,
              },
            },
            {
              invoiceNumber: {
                [Op.iLike]: `%${text}%`,
              },
            },
            {
              '$supplier.name$': {
                [Op.iLike]: `%${text}%`,
              },
            },
          ],
        },
      })

      const data: IPurchaseInvoiceResponse[] = []

      for (const item of rows) {
        data.push({
          ...item.get(),
          factoryGateId: item.dataValues.factoryGateId,
          supplier: item.supplier.dataValues.name,
          entryId: item.entryMapping?.dataValues.entryNumber ?? '',
        })
      }

      const totalPages = Math.ceil(count / pageSize)

      return HelperMethods.getSuccessResponse({
        currentPage: page,
        totalData: count,
        totalPages: totalPages,
        data: data,
      })
    } catch (error) {
      HelperMethods.handleError(error)
      return HelperMethods.getErrorResponse()
    }
  }

  async getById(
    id: number
  ): Promise<APIBaseResponse<IPurchaseInvoiceDetailedResponse | null>> {
    try {
      const result = await PurchaseInvoiceTable.findByPk(id, {
        include: [
          {
            model: SupplierTable,
            as: 'supplier',
          },
          {
            model: RawMaterialRejectionTable,
            as: 'rawMaterialRejections',
          },
          {
            model: RawMaterialHoldTable,
            as: 'rawMaterialHolds',
          },
          {
            model: DebitNoteTable,
            as: 'debitNotes',
            foreignKey: 'purchaseInvoiceId',
            include: [
              {
                model: RawMaterialVariationTable,
                as: 'rawMaterial',
              },
            ],
          },
          {
            model: RawMaterialExcessEntryTable,
            as: 'rawMaterialExcessEntry',
            required: false
          },
          {
            model: RawMaterialReplacementEntryTable,
            as: 'rawMaterialReplacementEntry',
            required: false
          }
        ],
      });

      if (!result) {
        return HelperMethods.getErrorResponse('No record found')
      }


      /* get purchase order */
      let linkedPurchaseOrder: PurchaseOrderTable | null = null;
      const linkedPurchaseMapping = await PurchaseInvoicePurchaseOrderMapping.findOne({
        where: {
          purchaseInvoiceId: id
        }
      });

      if (linkedPurchaseMapping) {
        linkedPurchaseOrder = await PurchaseOrderTable.findOne({
          where: {
            id: linkedPurchaseMapping.dataValues.purchaseOrderId
          }
        });
      }


      /* get raw materials */
      const receivedMaterial = await RawMaterialStockInTable.findAll({
        where: {
          purchaseInvoiceId: result.dataValues.id,
        },
        include: {
          model: RawMaterialVariationTable,
          as: 'rawMaterialVariation',
          paranoid: false,
          include: [
            {
              model: RawMaterialMainTable,
              as: 'rawMaterial',
              include: [
                {
                  model: ItemUnitTable,
                  as: 'unit',
                  attributes: ['name'],
                },
              ],
            },

          ],
        },
      })

      const rawMaterialVariations: IRawMaterialReceivedItemResponse[] = []

      let rejectionData: RawMaterialRejectionTable | null;
      let holdData: RawMaterialHoldTable | null;
      let excessEntry: RawMaterialExcessEntryTable | null;
      let replacementEntry: RawMaterialReplacementEntryTable | null;

      /* prepare data */
      for (const item of receivedMaterial) {
        rejectionData =
          result.rawMaterialRejections.find(
            (x) => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id
          ) ?? null

        holdData =
          result.rawMaterialHolds.find(
            (x) => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id
          ) ?? null

        excessEntry = result.rawMaterialExcessEntry.find(
          (x) => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id
        ) ?? null;

        replacementEntry = result.rawMaterialReplacementEntry.find(
          (x) => x.dataValues.rawMaterialId === item.rawMaterialVariation.dataValues.id
        ) ?? null;

        rawMaterialVariations.push({
          rawMaterialId: Number(item.rawMaterialVariation.dataValues.id),
          rawMaterial: item.rawMaterialVariation.dataValues.name,
          unit: item.rawMaterialVariation.rawMaterial.unit.dataValues.name,
          totalQty: Number(item.dataValues.qty),
          price: Number(item.dataValues.price),
          rejectedQty: Number(rejectionData?.dataValues?.rejectedQty ?? 0),
          rejectionReason: rejectionData?.dataValues?.rejectionReason ?? null,
          rejectedById: rejectionData?.dataValues?.rejectedById ? Number(rejectionData?.dataValues?.rejectedById) : null,
          holdQty: Number(holdData?.dataValues?.holdQty ?? 0),
          holdReason: holdData?.dataValues?.holdReason ?? null,
          holdById: holdData?.dataValues?.holdById ? Number(holdData?.dataValues?.holdById) : null,
          storageLocationId: item.dataValues.storageLocationId ? Number(item.dataValues.storageLocationId) : null,
          excessQty: Number(excessEntry?.dataValues?.qty ?? 0),
          replaceableQty: Number(replacementEntry?.dataValues?.qty ?? 0),
        })
      }


      const rejectionsData: IRawMaterialRejection[] = [];


      for (const item of result.rawMaterialRejections) {
        rejectionsData.push({
          ...item.dataValues,
          id: Number(item.dataValues.id),
          rawMaterialId: Number(item.dataValues.rawMaterialId),
          purchaseInvoiceId: Number(item.dataValues.purchaseInvoiceId),
          rejectedById: item.dataValues.rejectedById,
          totalQty: Number(item.dataValues.totalQty),
          rejectedQty: Number(item.dataValues.rejectedQty),

        });
      }

      return HelperMethods.getSuccessResponse({
        rawMaterials: rawMaterialVariations,
        supplier: {
          ...result.supplier.dataValues,
          id: Number(result.supplier.dataValues.id),
        },
        invoiceNumber: result.dataValues.invoiceNumber,
        invoiceDate: result.dataValues.invoiceDate,
        poNumber: result.dataValues.poNumber,
        poDate: result.dataValues.poDate,
        purchasedById: Number(result.dataValues.purchasedById),
        factoryGateId: Number(result.dataValues.factoryGateId),
        createdById: Number(result.dataValues.createdById),
        createdAt: result.dataValues.createdAt,
        updatedById: result.dataValues.updatedById ? Number(result.dataValues.updatedById) : null,
        updatedAt: result.dataValues.updatedAt,
        entryId: result.entryMapping?.dataValues.entryNumber ?? '',
        deletedAt: result.dataValues.deletedAt,
        deletedById: result.dataValues.deletedById ? Number(result.dataValues.deletedById) : null,
        status: result.dataValues.status,
        id: Number(result.dataValues.id),
        supplierId: Number(result.dataValues.supplierId),
        debitNotes: result.debitNotes,
        rawMaterialRejections: rejectionsData,
        purchaseOrder: linkedPurchaseOrder,
        rawMaterialExcessEntry: result.rawMaterialExcessEntry,
        rawMaterialReplacementEntry: result.rawMaterialReplacementEntry,
      });
    } catch (error) {
      HelperMethods.handleError(error)
      return HelperMethods.getErrorResponse()
    }
  }
}