import { NextFunction, Request, Response } from 'express';
import { RepoProvider } from '../../../core/RepoProvider';
import { get } from 'lodash';
import * as XLSX from 'xlsx';
import { sequelizeInit } from '../../../sequelize_init';
import { RawMaterialVariationTable } from '../../raw_material/database/RawMaterialVariationTable';
import { RawMaterialMainTable } from '../../raw_material/database/RawMaterialMainTable';
import { RAW_MATERIAL_STAUS } from '../../raw_material/models/RawMaterialMisc';
import { ItemAttributeTable } from '../../item_attribute/database/ItemAttributeTable';
import { ItemAttributeValueTable } from '../../item_attribute_value/database/ItemAttributeValueTable';
import { ITEM_ATTRIBUTES_VALUE_STATUS } from '../../item_attribute_value/models/ItemAttributeValueMisc';
import { RawMaterialVariationsAndAttributesRelationTable } from '../../raw_material/database/RawMaterialsAndAttributesRelationTable';

export class ExcelProcessorController {
  static async processExcel(req: Request, res: Response, next: NextFunction) {
    try {
      const userId = get(req, 'user_id');

      if (!req.file) {
        res.status(400).send({
          success: false,
          message: "No file uploaded",
          data: null
        });
        return;
      }

      /* Extract data from Excel file */
      const excelData = await ExcelProcessorController.extractExcelData(req.file);

      await ExcelProcessorController.doOperations(excelData, userId);

      const result = await RepoProvider.excelProcessorRepo.processExcel({
        file: req.file,
        createdById: Number(userId!),
      });

      if (!result.success) {
        res.status(500).send(result);
        return;
      }

      res.status(200).send(result);
    } catch (error) {
      console.error('Error processing Excel file:', error);
      res.status(500).send({
        success: false,
        message: "Error processing Excel file",
        data: null
      });
    }
  }


  static async extractExcelData(file: Express.Multer.File): Promise<any[]> {
    const data: any[] = [];
    const workbook = XLSX.readFile(file.path);
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];
    const rows: any = XLSX.utils.sheet_to_json(sheet);
    let i = 1;

    const transaction = await sequelizeInit.transaction();

    let isThickness = false;
    let isSize = false;
    let isCode = false;
    let isHardness = false;
    let isWeight = false;
    let isColor = false;
    let mainName = "";
    let variationName = "";
    let variationSKU = "";


    try {

      for (const row of rows) {


        mainName = row["main name"];
        variationName = row["name"];
        variationSKU = row["sku"];

        variationName = variationName.trim().toLowerCase();
        if (mainName) {
          mainName = mainName.trim().toLowerCase();
        }
        else {
          mainName = variationName;
        }

        if (variationSKU && variationSKU.length > 0) {
          variationSKU = variationSKU.trim().toLowerCase();
        }
        else {
          variationSKU = variationName + "_sku";
        }

        /* get raw material variation */
        const variation = await RawMaterialVariationTable.findByPk(row["id"], {
          transaction: transaction,
        });

        if (!variation) {
          console.log("Variation not found for id: ", row["id"]);
          continue;
        }
        // isThickness = row["thickness"] ? true : false;
        isSize = row["size"] ? true : false;
        // isCode = row["CODE"] ? true : false;
        // isHardness = row["hardness"] ? true : false;
        // isWeight = row["weight"] ? true : false;
        isColor = row["color"] ? true : false;

        // if (isThickness) {
        //   const thicknessAttribute = await ItemAttributeTable.findOne({
        //     where: {
        //       name: "thickness",
        //     },
        //     transaction: transaction,
        //   });

        //   let existingValue = await ItemAttributeValueTable.findOne({
        //     where: {
        //       itemAttributeId: thicknessAttribute!.dataValues.id,
        //       value: row["thickness"].toString().trim(),
        //     },
        //     transaction: transaction,
        //   });

        //   if (!existingValue) {
        //     existingValue = await ItemAttributeValueTable.create({
        //       itemAttributeId: thicknessAttribute!.dataValues.id,
        //       title: row["thickness"].toString().trim(),
        //       value: row["thickness"].toString().trim(),
        //       status: ITEM_ATTRIBUTES_VALUE_STATUS.ACTIVE,
        //       createdById: 4,
        //     }, {
        //       transaction: transaction,
        //       userId: 4,
        //     });
        //   }


        //   await RawMaterialVariationsAndAttributesRelationTable.create({
        //     rawMaterialId: row["id"],
        //     itemAttributeValueId: existingValue.dataValues.id,
        //     createdById: 4,
        //   }, {
        //     transaction: transaction,
        //     userId: 4,
        //   });

        // }
        if (isSize) {
          const thicknessAttribute = await ItemAttributeTable.findOne({
            where: {
              name: "size",
            },
            transaction: transaction,
          });

          let existingValue = await ItemAttributeValueTable.findOne({
            where: {
              itemAttributeId: thicknessAttribute!.dataValues.id,
              value: row["size"].toString().trim(),
            },
            transaction: transaction,
          });

          if (!existingValue) {
            existingValue = await ItemAttributeValueTable.create({
              itemAttributeId: thicknessAttribute!.dataValues.id,
              title: row["size"].toString().trim(),
              value: row["size"].toString().trim(),
              status: ITEM_ATTRIBUTES_VALUE_STATUS.ACTIVE,
              createdById: 4,
            }, {
              transaction: transaction,
              userId: 4,
            });
          }
          await RawMaterialVariationsAndAttributesRelationTable.create({
            rawMaterialVariationId: row["id"],
            itemAttributeValueId: existingValue.dataValues.id,
            createdById: 4,
          }, {
            transaction: transaction,
            userId: 4,
          });
        }
        // if (isCode) {
        //   const thicknessAttribute = await ItemAttributeTable.findOne({
        //     where: {
        //       name: "code",
        //     },
        //     transaction: transaction,
        //   });

        //   let existingValue = await ItemAttributeValueTable.findOne({
        //     where: {
        //       itemAttributeId: thicknessAttribute!.dataValues.id,
        //       value: row["CODE"].toString().trim(),
        //     },
        //     transaction: transaction,
        //   });

        //   if (!existingValue) {
        //     existingValue = await ItemAttributeValueTable.create({
        //       itemAttributeId: thicknessAttribute!.dataValues.id,
        //       title: row["CODE"].toString().trim(),
        //       value: row["CODE"].toString().trim(),
        //       status: ITEM_ATTRIBUTES_VALUE_STATUS.ACTIVE,
        //       createdById: 4,
        //     }, {
        //       transaction: transaction,
        //       userId: 4,
        //     });
        //   }
        //   await RawMaterialVariationsAndAttributesRelationTable.create({
        //     rawMaterialId: row["id"],
        //     itemAttributeValueId: existingValue.dataValues.id,
        //     createdById: 4,
        //   }, {
        //     transaction: transaction,
        //     userId: 4,
        //   });
        // }
        // if (isHardness) {
        //   const thicknessAttribute = await ItemAttributeTable.findOne({
        //     where: {
        //       name: "hardness",
        //     },
        //     transaction: transaction,
        //   });

        //   let existingValue = await ItemAttributeValueTable.findOne({
        //     where: {
        //       itemAttributeId: thicknessAttribute!.dataValues.id,
        //       value: row["hardness"].toString().trim(),
        //     },
        //     transaction: transaction,
        //   });

        //   if (!existingValue) {
        //     existingValue = await ItemAttributeValueTable.create({
        //       itemAttributeId: thicknessAttribute!.dataValues.id,
        //       title: row["hardness"].toString().trim(),
        //       value: row["hardness"].toString().trim(),
        //       status: ITEM_ATTRIBUTES_VALUE_STATUS.ACTIVE,
        //       createdById: 4,
        //     }, {
        //       transaction: transaction,
        //       userId: 4,
        //     });
        //   }
        //   await RawMaterialVariationsAndAttributesRelationTable.create({
        //     rawMaterialId: row["id"],
        //     itemAttributeValueId: existingValue.dataValues.id,
        //     createdById: 4,
        //   }, {
        //     transaction: transaction,
        //     userId: 4,
        //   });
        // }
        // if (isWeight) {
        //   const thicknessAttribute = await ItemAttributeTable.findOne({
        //     where: {
        //       name: "weight",
        //     },
        //     transaction: transaction,
        //   });

        //   let existingValue = await ItemAttributeValueTable.findOne({
        //     where: {
        //       itemAttributeId: thicknessAttribute!.dataValues.id,
        //       value: row["weight"].toString().trim(),
        //     },
        //     transaction: transaction,
        //   });

        //   if (!existingValue) {
        //     existingValue = await ItemAttributeValueTable.create({
        //       itemAttributeId: thicknessAttribute!.dataValues.id,
        //       title: row["weight"].toString().trim(),
        //       value: row["weight"].toString().trim(),
        //       status: ITEM_ATTRIBUTES_VALUE_STATUS.ACTIVE,
        //       createdById: 4,
        //     }, {
        //       transaction: transaction,
        //       userId: 4,
        //     });
        //   }
        //   await RawMaterialVariationsAndAttributesRelationTable.create({
        //     rawMaterialId: row["id"],
        //     itemAttributeValueId: existingValue.dataValues.id,
        //     createdById: 4,
        //   }, {
        //     transaction: transaction,
        //     userId: 4,
        //   });
        // }
        if (isColor) {
          const thicknessAttribute = await ItemAttributeTable.findOne({
            where: {
              name: "color",
            },
            transaction: transaction,
          });

          let existingValue = await ItemAttributeValueTable.findOne({
            where: {
              itemAttributeId: thicknessAttribute!.dataValues.id,
              value: row["color"].toString().trim(),
            },
            transaction: transaction,
          });

          if (!existingValue) {
            existingValue = await ItemAttributeValueTable.create({
              itemAttributeId: thicknessAttribute!.dataValues.id,
              title: row["color"].toString().trim(),
              value: row["color"].toString().trim(),
              status: ITEM_ATTRIBUTES_VALUE_STATUS.ACTIVE,
              createdById: 4,
            }, {
              transaction: transaction,
              userId: 4,
            });
          }
          await RawMaterialVariationsAndAttributesRelationTable.create({
            rawMaterialVariationId: row["id"],
            itemAttributeValueId: existingValue.dataValues.id,
            createdById: 4,
          }, {
            transaction: transaction,
            userId: 4,
          });
        }



        // <!--- raw materials related--- !>
        /* check if main raw material already exists by name */
        const existingMainRawMaterial = await RawMaterialMainTable.findOne({
          where: {
            name: mainName,
          },
          transaction: transaction,
        });

        if (existingMainRawMaterial) {
          /* update the variation */
          await RawMaterialVariationTable.update({
            parentRawMaterialId: existingMainRawMaterial.dataValues.id,
            name: variationName,
            sku: variationSKU,
            updatedById: 4,
          }, {
            where: {
              id: variation.dataValues.id,
            },
            transaction: transaction,
            userId: 4,
          });
          console.log("Done for row ", i++, "id: ", row["id"]);
          continue;
        }

        /* create main raw material*/
        // const mainRawMaterial = await RawMaterialMainTable.create({
        //   name: mainName,
        //   hsn: variation.dataValues.hsn,
        //   gstPercentage: variation.dataValues.gstPercentage,
        //   categoryId: variation.dataValues.categoryId,
        //   unitId: variation.dataValues.unitId,
        //   status: RAW_MATERIAL_STAUS.ACTIVE,
        //   createdById: 4,
        // }, {
        //   transaction: transaction,
        //   userId: 4,
        // });

        /* update the variation */
        // await RawMaterialVariationTable.update({
        //   parentRawMaterialId: mainRawMaterial.dataValues.id,
        //   name: variationName,
        //   sku: variationSKU,
        //   updatedById: 4,
        // }, {
        //   where: {
        //     id: variation.dataValues.id,
        //   },
        //   transaction: transaction,
        //   userId: 4,
        // });
        console.log("Done for row ", i++, "id: ", row["id"]);

      }


      await transaction.commit();
      console.log("ALL DONE");

    } catch (error) {
      await transaction.rollback();
      console.log(error);
    }



    return data;

  }

  static async doOperations(data: any[], userId: string | undefined): Promise<void> {
    for (const rawMaterial of data) {
      // const result = await RepoProvider.rawMaterialRepo.update(rawMaterial);
      // if (!result.success) {
      //   console.error('Error updating raw material:', result.message);
      // }
    }
  }
}
