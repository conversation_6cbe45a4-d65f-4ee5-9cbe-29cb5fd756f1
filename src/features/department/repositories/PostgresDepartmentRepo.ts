import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { HelperMethods } from "../../../core/HelperMethods";
import { sequelizeInit } from "../../../sequelize_init";
import { DepartmentTable } from "../database/DepartmentTable";
import { ICreateDepartment, IDepartmentResponse } from "../models/IDepartment";
import { DEPARTMENT_STATUS } from "../models/DepartmentMisc";
import { IDepartmentRepo } from "./IDepartmentRepo";
import { Op, UniqueConstraintError } from "sequelize";
import { CoreUserTable } from "../../users/core/database/CoreUserTable";

export class PostgresDepartmentRepo implements IDepartmentRepo {
    
    async create(payload: ICreateDepartment): Promise<APIBaseResponse<DepartmentTable | null>> {
        try {
            const result = await DepartmentTable.create(payload, {
                userId: payload.createdById,
            });
            return HelperMethods.getSuccessResponse(result);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse('Department already exists');
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async update(id: number, payload: ICreateDepartment): Promise<APIBaseResponse<null>> {
        try {
            const deletionUpdates = {
                deletedAt: new Date(),
                deletedById: payload.updatedById,
            };
            if (payload.status === DEPARTMENT_STATUS.DELETED) {
                Object.assign(payload, deletionUpdates);
            }
            await DepartmentTable.update(payload, {
                where: {
                    id: id
                },
                userId: payload.updatedById!,
                individualHooks: true,
            });
            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            if (error instanceof UniqueConstraintError) {
                return HelperMethods.getErrorResponse('Department already exists');
            }
            return HelperMethods.getErrorResponse();
        }
    }

    async getAll(page: number, pageSize: number): Promise<APIBaseResponse<PaginatedBaseResponse<IDepartmentResponse> | null>> {
        try {
            const offset = (page - 1) * pageSize;
            const { count, rows } = await DepartmentTable.findAndCountAll({
                limit: pageSize,
                offset: offset,
                order: [['createdAt', 'DESC']],
                where: {
                    status: DEPARTMENT_STATUS.ACTIVE,
                    deletedAt: null,
                },
                include: [
                    {
                        model: CoreUserTable,
                        as: "createdBy",
                        attributes: ["firstName", 'lastName'],
                    }
                ],
            });

            const totalPages = Math.ceil(count / pageSize);

            const data: IDepartmentResponse[] = rows.map(row => ({
                id: Number(row.dataValues.id),
                name: row.dataValues.name,
                status: row.dataValues.status,
                createdBy: row.createdBy.dataValues.firstName + ' ' + row.createdBy.dataValues.lastName,
                createdAt: row.dataValues.createdAt,
            }));

            return HelperMethods.getSuccessResponse({
                currentPage: page,
                totalData: count,
                totalPages: totalPages,
                data: data,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async searchByText(text: string): Promise<APIBaseResponse<PaginatedBaseResponse<IDepartmentResponse> | null>> {
        try {
            const { count, rows } = await DepartmentTable.findAndCountAll({
                limit: 10,
                order: [['createdAt', 'DESC']],
                where: {
                    name: {
                        [Op.iLike]: `%${text}%`
                    },
                    status: DEPARTMENT_STATUS.ACTIVE
                },
            });

            const totalPages = 1;

            const data: IDepartmentResponse[] = rows.map(row => ({
                id: Number(row.dataValues.id),
                name: row.dataValues.name,
                status: row.dataValues.status,
                createdBy: "",
                createdAt: row.dataValues.createdAt,
                createdById: row.dataValues.createdById,
                updatedAt: row.dataValues.updatedAt,
                updatedById: row.dataValues.updatedById,
                deletedAt: row.dataValues.deletedAt,
                deletedById: row.dataValues.deletedById,
            }));

            return HelperMethods.getSuccessResponse({
                currentPage: 1,
                totalData: count,
                totalPages: totalPages,
                data: data,
            });
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async getById(id: number): Promise<APIBaseResponse<IDepartmentResponse | null>> {
        try {
            const result = await DepartmentTable.findByPk(id, {
                include: [
                    {
                        model: CoreUserTable,
                        as: "createdBy",
                        attributes: ["firstName", 'lastName'],
                    }
                ],
            });
            if (!result) {
                return HelperMethods.getErrorResponse('Department not found');
            }

            const data: IDepartmentResponse = {
                id: Number(result.dataValues.id),
                name: result.dataValues.name,
                status: result.dataValues.status,
                createdBy: result.createdBy.dataValues.firstName + ' ' + result.createdBy.dataValues.lastName,
                createdAt: result.dataValues.createdAt,
            };

            return HelperMethods.getSuccessResponse(data);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }

    async delete(ids: number[], deletedById: number): Promise<APIBaseResponse<null>> {
        try {
            await DepartmentTable.update(
                {
                    status: DEPARTMENT_STATUS.DELETED,
                    deletedAt: new Date(),
                    deletedById: Number(deletedById),
                },
                {
                    where: {
                        id: {
                            [Op.in]: ids
                        }
                    },
                    userId: Number(deletedById),
                    individualHooks: true,
                }
            );
            return HelperMethods.getSuccessResponse(null);
        } catch (error) {
            HelperMethods.handleError(error);
            return HelperMethods.getErrorResponse();
        }
    }
}
