import { APIBaseResponse, PaginatedBaseResponse } from "../../../core/CoreInterfaces";
import { DepartmentTable } from "../database/DepartmentTable";
import { ICreateDepartment, IDepartmentResponse } from "../models/IDepartment";

export interface IDepartmentRepo {
    create(payload: ICreateDepartment): Promise<APIBaseResponse<DepartmentTable | null>>;

    update(id: number, payload: ICreateDepartment): Promise<APIBaseResponse<void>>;

    getAll(page: number, pageSize: number): Promise<APIBaseResponse<PaginatedBaseResponse<IDepartmentResponse> | null>>;

    searchByText(text: string): Promise<APIBaseResponse<PaginatedBaseResponse<IDepartmentResponse> | null>>;

    getById(id: number): Promise<APIBaseResponse<IDepartmentResponse | null>>;

    delete(ids: number[], deletedById: number): Promise<APIBaseResponse<null>>;
}
